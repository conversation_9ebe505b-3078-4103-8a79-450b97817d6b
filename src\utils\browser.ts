import { Browser, chromium } from 'playwright';
import config from '../config';
class BrowserManager {
    private static instance: Browser | null = null;
    private static isInitializing = false;
    private static initPromise: Promise<Browser> | null = null;

    static async getInstance(): Promise<Browser> {
        if (this.instance && this.instance.isConnected()) {
            return this.instance;
        }

        if (this.isInitializing) {
            return this.initPromise!;
        }

        this.isInitializing = true;
        this.initPromise = this.initializeBrowser();
        
        try {
            this.instance = await this.initPromise;
            return this.instance;
        } finally {
            this.isInitializing = false;
            this.initPromise = null;
        }
    }

    private static async initializeBrowser(): Promise<Browser> {
        return chromium.launch({
            headless: config.browser.headless,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-infobars',
                '--window-size=1920,1080'
            ],
            executablePath: process.env.PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH,
        });
    }

    static async closeBrowser() {
        if (this.instance) {
            await this.instance.close();
            this.instance = null;
        }
    }
}

export default BrowserManager;