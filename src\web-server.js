import { H3, serve, serveStatic } from 'h3';
import { stat, readFile } from 'node:fs/promises';
import { join } from 'node:path';
import { authMiddleware } from './middlewares/auth.js';
import { corsMiddleware } from './middlewares/cors.js';
import { healthHandler } from './routes/health.js';
import config from './config.js';


const app = new H3();
 

// 全局 CORS 中间件
app.use(corsMiddleware);
app.use(authMiddleware);
// --- API 端点 ---
// 健康检查端点
app.get('/health', healthHandler);

// 启动服务器
const port = config.server.port;
const host = config.server.host;
serve(app, { port, host });
info(`🚀 服务器运行在 http://${host}:${port}`);
info(`🏠 管理面板: http://${host}:${port}/`);
info(`📋 健康检查: http://${host}:${port}/health`);
info(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
 