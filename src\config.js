import dotenv from 'dotenv';

// 加载环境变量文件
// 先加载 .env 文件（默认配置）
dotenv.config({ path: '.env' });
// 再加载 .env.local 文件（本地覆盖配置，优先级更高）
dotenv.config({ path: '.env.local' });
 
 


const config = {
  // 服务器配置
  server: {
    port: parseInt(process.env.PORT || '7860', 10),
    host: process.env.HOST || '0.0.0.0'
  },

  // Playwright 浏览器配置
  browser: {
    // 浏览器类型：chromium 或 camoufox
    type: browserType,
    headless: (process.env.HEADLESS || 'true').toLowerCase() !== 'false',
    timeout: parseInt(process.env.TIMEOUT || '60000', 10), // 增加到60秒
    // 根据浏览器类型选择可执行文件路径
    executablePath: (() => {
      if (browserType === 'camoufox') {
        // camoufox.js会使用os.homedir()获取用户主目录
        return undefined;
      }
      return process.env.PLAYWRIGHT_EXECUTABLE_PATH;
    })(),

    args: (() => {
      const baseArgs = [
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-infobars',
        '--disable-extensions',
      ];

      // 如果是 chromium，添加防检测参数
      if (browserType === 'chromium') {
        return [
          '--disable-blink-features=AutomationControlled',
          ...baseArgs
        ];
      }
      // camoufox Docker环境优化参数
      if (browserType === 'camoufox') {
        return [
          ...baseArgs
        ];
      }
      // patchright 使用默认参数（Patchright 会自动处理防检测）
      if (browserType === 'patchright') {
        return baseArgs;
      }
      return baseArgs;
    })()
  },
  // app 配置
  app: { 
    token: process.env.API_TOKEN,
  },
};

export default config;