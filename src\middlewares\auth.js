import { createError } from 'h3';
import config from '../config.js';


/**
 * CORS (跨域资源共享) 中间件。
 * 使用 H3 内置的 CORS 处理功能。
 * @param {import('h3').H3Event} event - H3 事件对象。
 * @param {import('h3').next} next - 下一个中间件函数。
 * @returns {Response|void} 如果是 OPTIONS 预检请求，则返回处理结果。
 */

export async function authMiddleware(event, next) {
  const authHeader = event.req.headers.get("authorization");
  if (!authHeader) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized: Missing Authorization header.'
    });
  }

  const [authType, token] = authHeader.split(' ');
  if (authType !== 'Bearer' || !token) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized: Invalid Authorization header format. Expected: Bearer <token>.'
    });
  }

  if (token !== config.app.token) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized: Invalid API token.'
    });
  }

  const rawBody = await next();
  // [intercept response]
  return rawBody;
}

